import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

# 1. Importe os dados de medical_examination.csv e atribuí-lo ao df variável
df = pd.read_csv('medical_examination.csv')

# 2. Adiciona um overweight coluna para os dados
# Calcula IMC: peso(kg) / altura(m)²
# Se IMC > 25, overweight = 1, senão overweight = 0
df['overweight'] = (df['weight'] / (df['height'] / 100) ** 2 > 25).astype(int)

# 3. Normaliza os dados fazendo 0 sempre bom e 1 sempre ruim
# Se cholesterol ou gluc é 1, defina para 0. Se > 1, defina para 1
df['cholesterol'] = (df['cholesterol'] > 1).astype(int)
df['gluc'] = (df['gluc'] > 1).astype(int)

# 4. Desenha o gráfico categórico no draw_cat_plot função
def draw_cat_plot():
    # 5. Cria um DataFrame para o gráfico do gato usando pd.melt
    df_cat = pd.melt(df, id_vars=['cardio'], value_vars=['cholesterol', 'gluc', 'smoke', 'alco', 'active', 'overweight'])

    # 6. Agrupar e reformatar os dados em df_cat para dividi-lo por cardio
    # Mostrar as contagens de cada característica
    df_cat = df_cat.groupby(['cardio', 'variable', 'value']).size().reset_index(name='total')

    # 7. Converta os dados em long format e crie um gráfico
    fig = sns.catplot(data=df_cat, x='variable', y='total', hue='value', col='cardio', kind='bar')

    # 8. Obtenha o valor da saída e armazene-o no fig variável
    fig = fig.fig

    fig.savefig('catplot.png')
    return fig

# 9. Desenha o Mapa de Calor no draw_heat_map função
def draw_heat_map():
    # 10. Limpa os dados no df_heat variável filtrando segmentos incorretos
    df_heat = df[
        (df['ap_lo'] <= df['ap_hi']) &  # pressão diastólica <= sistólica
        (df['height'] >= df['height'].quantile(0.025)) &  # altura >= percentil 2.5
        (df['height'] <= df['height'].quantile(0.975)) &  # altura <= percentil 97.5
        (df['weight'] >= df['weight'].quantile(0.025)) &  # peso >= percentil 2.5
        (df['weight'] <= df['weight'].quantile(0.975))    # peso <= percentil 97.5
    ]

    # 11. Calcula a matriz de correlação e armazene-a no corr variável
    corr = df_heat.corr()

    # 12. Gera uma máscara para o triângulo superior e armazene-a no mask variável
    mask = np.triu(np.ones_like(corr, dtype=bool))

    # 13. Configura o matplotlib figura
    fig, ax = plt.subplots(figsize=(12, 9))

    # 14. Traca a matriz de correlação usando sns.heatmap()
    sns.heatmap(corr, mask=mask, annot=True, fmt='.1f', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": 0.5})

    fig.savefig('heatmap.png')
    return fig

