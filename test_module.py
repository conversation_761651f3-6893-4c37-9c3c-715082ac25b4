import unittest
import medical_data_visualizer
import matplotlib as mpl

# Test your function by calling it here
class CatPlotTestCase(unittest.TestCase):
    def setUp(self):
        self.fig = medical_data_visualizer.draw_cat_plot()

    def test_line_plot_labels(self):
        actual = self.fig.axes[0].get_xlabel()
        expected = "variable"
        self.assertEqual(actual, expected, "Expected line plot xlabel to be 'variable'")
        
    def test_line_plot_title(self):
        actual = self.fig.axes[0].get_title()
        expected = "cardio = 0"
        self.assertEqual(actual, expected, "Expected line plot title to be 'cardio = 0'")

class HeatMapTestCase(unittest.TestCase):
    def setUp(self):
        self.fig = medical_data_visualizer.draw_heat_map()

    def test_heat_map_labels(self):
        actual = []
        expected = ['id', 'age', 'sex', 'height', 'weight', 'ap_hi', 'ap_lo', 'cholesterol', 'gluc', 'smoke', 'alco', 'active', 'cardio', 'overweight']
        actual = [label.get_text() for label in self.fig.axes[0].get_xticklabels()]
        self.assertEqual(actual, expected, "Expected bar plot legend labels to be months.")
        
    def test_heat_map_values(self):
        actual = [text.get_text() for text in self.fig.axes[0].texts]
        expected = ['0.0', '0.0', '-0.0', '0.0', '-0.1', '-0.1', '0.0', '0.1', '0.1', '0.3', '0.0', '0.2', '-0.0', '0.1']
        self.assertEqual(actual, expected, "Expected heat map values to be different.")

if __name__ == "__main__":
    unittest.main()
